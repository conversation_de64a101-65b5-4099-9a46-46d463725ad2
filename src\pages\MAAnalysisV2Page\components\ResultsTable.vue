<template>
  <el-card class="results-table-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <h3>各周期回测结果统计</h3>
      </div>
    </template>
    <el-table :data="results" style="width: 100%" border stripe v-loading="loading">
      <el-table-column prop="period" label="均线周期" width="100" sortable></el-table-column>
      <el-table-column prop="profit_rate" label="收益率(%)" width="120" sortable>
        <template #default="{ row }">
          <span :class="row.profit_rate > 0 ? 'profit' : 'loss'">{{ row.profit_rate.toFixed(2) }}%</span>
        </template>
      </el-table-column>
      <el-table-column prop="max_drawdown" label="最大回撤(%)" width="140" sortable>
         <template #default="{ row }">
          <span>{{ row.max_drawdown.toFixed(2) }}%</span>
        </template>
      </el-table-column>
      <el-table-column prop="annualized_return" label="年化收益率(%)" width="150" sortable>
        <template #default="{ row }">
          <span :class="row.annualized_return > 0 ? 'profit' : 'loss'">{{ row.annualized_return.toFixed(2) }}%</span>
        </template>
      </el-table-column>
      <el-table-column prop="transaction_count" label="交易次数" width="110" sortable></el-table-column>
       <el-table-column prop="three_stage_investment_count" label="三阶段投资次数" width="160" sortable></el-table-column>
      <el-table-column prop="investment_failure_count" label="投资失败次数" width="150" sortable></el-table-column>
      <el-table-column prop="total_profit" label="总利润" width="120" sortable>
         <template #default="{ row }">
          <span :class="row.total_profit > 0 ? 'profit' : 'loss'">{{ row.total_profit.toFixed(2) }}</span>
        </template>
      </el-table-column>
    </el-table>
  </el-card>
</template>

<script setup>
import { defineProps } from 'vue';

const props = defineProps({
  results: {
    type: Array,
    required: true,
  },
  loading: {
    type: Boolean,
    default: false,
  }
});
</script>

<style scoped>
.results-table-card {
  margin-top: 20px;
}
.profit {
  color: #67c23a;
}
.loss {
  color: #f56c6c;
}
</style> 