<template>
  <el-card class="capital-curve-chart-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <h3>资金曲线 (MA{{ period }})</h3>
      </div>
    </template>
    <div ref="chartRef" style="width: 100%; height: 400px;"></div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, defineProps } from 'vue';
import * as echarts from 'echarts';

const props = defineProps({
  simulationResult: {
    type: Object,
    required: true,
  },
  initialCapital: {
    type: Number,
    required: true,
  }
});

const chartRef = ref(null);
let chartInstance = null;

const period = computed(() => props.simulationResult?.period || '');

const prepareChartData = () => {
  if (!props.simulationResult || !props.simulationResult.daily_logs) {
    return { dates: [], values: [] };
  }
  
  const dates = props.simulationResult.daily_logs.map(log => log.date);
  const values = props.simulationResult.daily_logs.map(log => {
    // 总资产 = 初始资金 + 累计已实现盈亏 + 当日浮动盈亏
    const totalAsset = props.initialCapital + log.cumulative_realized_pnl + log.daily_pnl;
    return totalAsset.toFixed(2);
  });

  return { dates, values };
};

const renderChart = () => {
  const { dates, values } = prepareChartData();
  if (!chartInstance || dates.length === 0) return;

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: ['总资产']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: dates
    },
    yAxis: {
      type: 'value',
      scale: true,
      axisLabel: {
        formatter: '{value} 元'
      }
    },
    series: [
      {
        name: '总资产',
        type: 'line',
        smooth: true,
        data: values,
        itemStyle: {
          color: '#409EFF'
        }
      }
    ]
  };

  chartInstance.setOption(option);
};

onMounted(() => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
    renderChart();
  }
});

watch(() => props.simulationResult, () => {
  renderChart();
}, { deep: true });
</script>

<style scoped>
.capital-curve-chart-card {
  margin-top: 20px;
}
</style> 