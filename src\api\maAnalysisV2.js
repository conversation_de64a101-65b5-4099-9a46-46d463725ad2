import request from './request';

/**
 * 运行均线分析V2策略回测
 * @param {object} params - 查询参数
 * @param {string} params.stock_code - 股票代码
 * @param {string} params.start_date - 开始日期
 * @param {string} params.end_date - 结束日期
 * @param {number} params.min_period - 最小周期
 * @param {number} params.max_period - 最大周期
 * @param {number} params.step - 步长
 * @param {number} params.initial_capital - 初始资金
 * @returns 
 */
export function simulateMAV2Investment(params) {
  return request({
    url: '/ma_analysis_v2/simulate',
    method: 'get',
    params
  });
} 