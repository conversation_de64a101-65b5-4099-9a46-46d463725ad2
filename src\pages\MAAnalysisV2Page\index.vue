<template>
  <div class="ma-analysis-v2-page">
    <page-header title="均线分析V2 - 三阶段建仓策略回测" subtitle="根据三阶段动态建仓与盈利保护策略进行投资回测" />
    
    <el-card class="query-card">
      <el-form :model="queryForm" ref="queryFormRef" label-width="100px" size="default">
        <el-row :gutter="20">
          <el-col :xs="24" :sm="8">
            <el-form-item label="股票代码" prop="stock_code">
              <el-input v-model="queryForm.stock_code" placeholder="请输入股票代码，如：000001.SZ" clearable />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-form-item label="开始日期" prop="start_date">
              <el-date-picker v-model="queryForm.start_date" type="date" placeholder="选择开始日期" value-format="YYYY-MM-DD" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-form-item label="结束日期" prop="end_date">
              <el-date-picker v-model="queryForm.end_date" type="date" placeholder="选择结束日期" value-format="YYYY-MM-DD" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :xs="24" :sm="8">
            <el-form-item label="最小周期" prop="min_period">
              <el-input-number v-model="queryForm.min_period" :min="1" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-form-item label="最大周期" prop="max_period">
              <el-input-number v-model="queryForm.max_period" :min="queryForm.min_period" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :xs="24" :sm="8">
            <el-form-item label="周期步长" prop="step">
              <el-input-number v-model="queryForm.step" :min="1" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item>
          <el-button type="primary" @click="handleQuery" :loading="loading">
            <el-icon><Search /></el-icon> 开始回测
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <div v-if="loading" class="loading-spinner">
      <el-card>
        <el-skeleton :rows="5" animated />
        <p>正在努力回测中，请稍候...</p>
      </el-card>
    </div>

    <div v-if="!loading && simulationResults.length > 0">
      <ResultsTable :results="simulationResults" />

      <el-tabs v-model="activePeriod" type="border-card" class="result-tabs">
        <el-tab-pane v-for="result in simulationResults" :key="result.period" :label="`MA ${result.period}`" :name="result.period">
          <div v-if="activePeriod === result.period">
            <el-row :gutter="20">
              <el-col :span="12">
                <CapitalCurveChart :simulationResult="result" :initialCapital="queryForm.initial_capital" />
              </el-col>
              <el-col :span="12">
                <KLineChart :simulationResult="result" :stockCode="queryForm.stock_code" :startDate="queryForm.start_date" :endDate="queryForm.end_date" />
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Search } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import PageHeader from '@/components/PageHeader/index.vue';
import ResultsTable from './components/ResultsTable.vue';
import CapitalCurveChart from './components/CapitalCurveChart.vue';
import KLineChart from './components/KLineChart.vue';
import { simulateMAV2Investment } from '@/api/maAnalysisV2'; // 导入API函数

// State
const queryFormRef = ref(null);
const loading = ref(false);
const simulationResults = ref([]);
const activePeriod = ref(null);

const queryForm = reactive({
  stock_code: '000001.SZ',
  start_date: '',
  end_date: '',
  min_period: 5,
  max_period: 20,
  step: 5,
  initial_capital: 100000,
});

// Set default dates
onMounted(() => {
  const today = new Date();
  const endDate = today.toISOString().split('T')[0];
  today.setFullYear(today.getFullYear() - 1);
  const startDate = today.toISOString().split('T')[0];
  queryForm.start_date = startDate;
  queryForm.end_date = endDate;
});

// Actions
const handleQuery = async () => {
  loading.value = true;
  simulationResults.value = [];
  try {
    const response = await simulateMAV2Investment(queryForm);
    if (response && response.length > 0) {
      simulationResults.value = response;
      activePeriod.value = response[0].period; // 默认选中第一个周期的标签页
      ElMessage.success('回测成功！');
    } else {
      ElMessage.warning('未返回任何回测结果。');
    }
  } catch (error) {
    ElMessage.error('回测失败，请检查参数或查看控制台。');
    console.error(error);
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.query-card {
  margin-bottom: 20px;
}
.loading-spinner {
  text-align: center;
  padding: 40px;
}
.result-tabs {
  margin-top: 20px;
}
</style> 