<template>
  <el-card class="k-line-chart-card" shadow="hover">
    <template #header>
      <div class="card-header">
        <h3>K线图与买卖点 (MA{{ period }})</h3>
      </div>
    </template>
    <div ref="chartRef" style="width: 100%; height: 500px;"></div>
  </el-card>
</template>

<script setup>
import { ref, onMounted, watch, defineProps, computed } from 'vue';
import * as echarts from 'echarts';
import { getStockData } from '@/api/stock'; // 假设通用K线数据API函数

const props = defineProps({
  simulationResult: {
    type: Object,
    required: true,
  },
  stockCode: {
    type: String,
    required: true,
  },
  startDate: {
    type: String,
    required: true,
  },
  endDate: {
    type: String,
    required: true,
  }
});

const chartRef = ref(null);
let chartInstance = null;
const klineData = ref([]);

const period = computed(() => props.simulationResult?.period || '');

const prepareChartData = () => {
  const dates = klineData.value.map(item => item.trade_date);
  const ohlc = klineData.value.map(item => [item.open, item.close, item.low, item.high]);
  
  const buyPoints = props.simulationResult.transactions
    .filter(t => t.action === 'buy')
    .map(t => ({
      name: '买入',
      coord: [t.trade_date, t.price],
      value: t.shares,
      itemStyle: { color: '#f56c6c' }
    }));

  const sellPoints = props.simulationResult.transactions
    .filter(t => t.action === 'sell')
    .map(t => ({
      name: '卖出',
      coord: [t.trade_date, t.price],
      value: t.shares,
      itemStyle: { color: '#67c23a' }
    }));

  return { dates, ohlc, buyPoints, sellPoints };
};

const renderChart = () => {
  if (!chartInstance || klineData.value.length === 0) return;
  const { dates, ohlc, buyPoints, sellPoints } = prepareChartData();

  const option = {
    tooltip: { trigger: 'axis', axisPointer: { type: 'cross' } },
    legend: { data: ['K线', '买入', '卖出'] },
    grid: { left: '10%', right: '10%', bottom: '15%' },
    xAxis: { type: 'category', data: dates, scale: true },
    yAxis: { type: 'value', scale: true },
    dataZoom: [{ type: 'inside', start: 50, end: 100 }, { show: true }],
    series: [
      {
        name: 'K线',
        type: 'candlestick',
        data: ohlc
      },
      {
        name: '买入',
        type: 'scatter',
        symbol: 'arrow',
        symbolSize: 15,
        symbolRotate: 180,
        data: buyPoints.map(p => p.coord),
        itemStyle: { color: '#f56c6c' }
      },
      {
        name: '卖出',
        type: 'scatter',
        symbol: 'arrow',
        symbolSize: 15,
        data: sellPoints.map(p => p.coord),
        itemStyle: { color: '#67c23a' }
      }
    ]
  };

  chartInstance.setOption(option);
};

const fetchKlineData = async () => {
  try {
    klineData.value = await getStockData(props.stockCode, props.startDate, props.endDate);
    renderChart();
  } catch (error) {
    console.error("获取K线数据失败:", error);
  }
};

onMounted(() => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
    fetchKlineData();
  }
});

watch(() => props.simulationResult, () => {
  fetchKlineData();
}, { deep: true });
</script>

<style scoped>
.k-line-chart-card {
  margin-top: 20px;
}
</style> 